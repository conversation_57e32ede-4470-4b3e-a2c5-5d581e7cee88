# 交互式回测程序新功能说明

## 概述

在原有的交互式回测程序基础上，新增了**执行模式选择**功能，允许用户选择执行全过程回测还是只测试择时信号，提高了程序的灵活性和效率。

## 新增功能

### 1. 执行模式选择

程序现在提供两种执行模式：

#### 模式1：完整回测 (step1-step4)
- **功能**：执行完整的回测流程
- **步骤**：数据准备 → 因子计算 → 选股 → 模拟交易
- **适用场景**：
  - 首次运行策略
  - 修改了策略参数（因子、过滤条件等）
  - 需要重新生成选股结果

#### 模式2：仅测试择时信号 (step4)
- **功能**：基于已有选股结果，只执行模拟交易和择时测试
- **步骤**：模拟交易（包含择时信号测试）
- **适用场景**：
  - 测试不同择时参数的效果
  - 优化择时策略
  - 快速验证择时信号表现

### 2. 智能前置条件检查

当选择"仅测试择时信号"模式时，程序会自动检查：

- **单阶段策略**：检查 `{策略名称}选股结果.pkl` 文件是否存在
- **两阶段策略**：检查两个阶段的选股结果文件是否都存在

如果缺少必要文件，程序会：
- 显示缺少的文件列表
- 建议用户先执行完整回测
- 安全退出，避免运行时错误

### 3. 支持单阶段和两阶段策略

新功能完全兼容现有的策略类型：

- **单阶段策略**：直接使用策略的选股结果
- **两阶段策略**：使用最终策略(strategy_2)的选股结果进行择时测试

## 使用流程

### 完整使用流程

1. **启动程序**
   ```bash
   python 交互式回测程序.py
   ```

2. **选择策略配置**
   - 从可用配置列表中选择策略
   - 可以预览配置详情

3. **选择执行模式**
   - 选择1：完整回测
   - 选择2：仅测试择时信号

4. **执行回测**
   - 程序根据选择的模式执行相应步骤
   - 显示执行进度和结果

### 择时参数优化工作流

1. **首次运行**：选择"完整回测"生成选股结果
2. **择时优化**：
   - 修改配置文件中的 `equity_timing` 参数
   - 选择"仅测试择时信号"
   - 快速获得新择时参数的回测结果
3. **重复步骤2**：测试不同择时参数组合

## 技术实现

### 新增函数

1. **`select_execution_mode()`**
   - 提供用户友好的模式选择界面
   - 返回选择的执行模式

2. **`check_step4_prerequisites(config_info)`**
   - 检查执行step4所需的前置条件
   - 支持单阶段和两阶段策略
   - 返回检查结果和缺失文件列表

3. **`run_step4_only(config_info)`**
   - 只执行step4的逻辑
   - 加载已有选股结果
   - 执行模拟交易和择时测试

### 修改的函数

1. **`run_backtest(config_info, execution_mode='full')`**
   - 新增 `execution_mode` 参数
   - 根据模式调用不同的执行函数
   - 增加前置条件检查

2. **`main()`**
   - 在配置选择后添加模式选择
   - 将模式参数传递给回测函数

## 优势

### 1. 提高效率
- 择时参数优化时无需重复执行前3个步骤
- 大幅减少计算时间
- 专注于择时策略的快速迭代

### 2. 增强灵活性
- 用户可根据需求选择合适的执行模式
- 支持不同的使用场景
- 保持向后兼容性

### 3. 改善用户体验
- 清晰的模式说明和使用指导
- 智能的错误检查和提示
- 友好的交互界面

## 注意事项

1. **文件依赖**：选择"仅测试择时信号"前，确保已执行过完整回测
2. **配置一致性**：择时测试使用的配置应与生成选股结果时的配置一致
3. **两阶段策略**：择时测试使用最终策略(strategy_2)的结果

## 示例

### 择时参数优化示例

```python
# 1. 首次运行 - 完整回测
# 选择配置：config_周黎明.py
# 选择模式：完整回测 (step1-step4)

# 2. 修改择时参数
# 编辑 configs/config_周黎明.py
equity_timing = {
    "name": "止损择时", 
    "params": [3, -0.05]  # 修改止损阈值
}

# 3. 快速测试新参数
# 选择配置：config_周黎明.py  
# 选择模式：仅测试择时信号 (step4)

# 4. 重复步骤2-3，测试不同参数组合
```

这样的工作流程可以大大提高择时策略的优化效率。
