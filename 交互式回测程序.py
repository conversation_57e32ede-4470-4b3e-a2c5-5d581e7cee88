"""
邢不行™️选股框架 - 简化版交互式回测程序
专注于交互式配置选择，避免复杂功能

特点：
- 交互式配置选择
- 简单可靠，不易出错
- 过滤掉特殊的两阶段策略
- 直接调用原有回测逻辑
"""

import warnings
warnings.filterwarnings("ignore")

import sys
import importlib.util
from pathlib import Path

from core.model.backtest_config import load_config

class SimpleConfigSelector:
    """简化版配置选择器"""
    
    def __init__(self):
        self.configs_dir = Path('configs')
        self.excluded_configs = []  # 不再排除任何策略，支持两阶段策略
    
    def scan_configs(self):
        """扫描可用的单阶段配置"""
        configs = []
        
        # 添加默认配置
        try:
            import config as default_config
            configs.append({
                'index': 0,
                'name': 'default',
                'file': 'config.py',
                'display_name': '默认配置 (config.py)',
                'strategy_name': default_config.strategy.get('name', '默认策略'),
                'hold_period': default_config.strategy.get('hold_period', 'N/A'),
                'select_num': default_config.strategy.get('select_num', 'N/A'),
                'description': '当前config.py中的配置'
            })
        except Exception as e:
            print(f"⚠️  默认配置加载失败: {e}")
        
        # 扫描configs目录，排除特殊配置
        if self.configs_dir.exists():
            index = 1
            for config_file in sorted(self.configs_dir.glob('config_*.py')):
                config_name = config_file.stem.replace('config_', '')
                
                # 跳过排除的配置
                if config_name in self.excluded_configs:
                    continue
                
                try:
                    config_module = self._load_config_module(str(config_file))

                    # 检测是否为两阶段策略
                    is_two_stage = (hasattr(config_module, 'strategy_1') and
                                   hasattr(config_module, 'strategy_2'))

                    if is_two_stage:
                        # 两阶段策略
                        strategy_1 = config_module.strategy_1
                        strategy_2 = config_module.strategy_2
                        display_name = f"{config_name} (两阶段)"
                        strategy_name = f"{strategy_2.get('name', '未命名')} [两阶段]"
                        description = f"两阶段策略: {strategy_1.get('name', '预处理')} → {strategy_2.get('name', '最终')}"
                        hold_period = strategy_2.get('hold_period', 'N/A')
                        select_num = strategy_2.get('select_num', 'N/A')
                    else:
                        # 单阶段策略
                        strategy = config_module.strategy
                        display_name = config_name
                        strategy_name = strategy.get('name', '未命名')
                        description = f"策略: {strategy.get('name', '未命名')}"
                        hold_period = strategy.get('hold_period', 'N/A')
                        select_num = strategy.get('select_num', 'N/A')

                    configs.append({
                        'index': index,
                        'name': config_name,
                        'file': str(config_file),
                        'display_name': display_name,
                        'strategy_name': strategy_name,
                        'hold_period': hold_period,
                        'select_num': select_num,
                        'description': description,
                        'is_two_stage': is_two_stage
                    })
                    index += 1
                    
                except Exception as e:
                    print(f"⚠️  配置 {config_name} 加载失败: {str(e)[:50]}...")
        
        return configs
    
    def _load_config_module(self, config_path):
        """加载配置模块"""
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        return config_module
    
    def display_configs(self, configs):
        """显示配置列表"""
        print("\n" + "="*70)
        print("🎯 邢不行™️选股框架 - 交互式策略选择")
        print("="*70)
        print("\n📋 可用策略配置:")
        print("-" * 70)
        
        for config in configs:
            print(f"  [{config['index']}] {config['display_name']}")
            print(f"      📊 策略: {config['strategy_name']}")
            print(f"      ⏰ 周期: {config['hold_period']}")
            print(f"      🎯 选股: {config['select_num']}")
            print(f"      📁 文件: {config['file']}")
            print()
    
    def preview_config(self, config_info):
        """预览配置详情"""
        try:
            if config_info['name'] == 'default':
                import config as config_module
            else:
                config_module = self._load_config_module(config_info['file'])
            
            print(f"\n📊 配置预览: {config_info['display_name']}")
            print("-" * 50)
            
            # 基本信息
            print(f"📅 回测时间: {getattr(config_module, 'start_date', 'N/A')} ~ {getattr(config_module, 'end_date', 'N/A')}")
            print(f"💰 初始资金: {getattr(config_module, 'initial_cash', 'N/A'):,}")
            
            # 策略信息
            if config_info.get('is_two_stage', False):
                # 两阶段策略预览
                strategy_1 = config_module.strategy_1
                strategy_2 = config_module.strategy_2

                print(f"\n🎯 两阶段策略信息:")
                print(f"  📋 阶段1 (预处理): {strategy_1.get('name', 'N/A')}")
                print(f"     持仓周期: {strategy_1.get('hold_period', 'N/A')}")
                print(f"     选股数量: {strategy_1.get('select_num', 'N/A')}")
                print(f"     选股因子: {len(strategy_1.get('factor_list', []))}个")
                print(f"     过滤条件: {len(strategy_1.get('filter_list', []))}个")

                print(f"  🎯 阶段2 (最终): {strategy_2.get('name', 'N/A')}")
                print(f"     持仓周期: {strategy_2.get('hold_period', 'N/A')}")
                print(f"     选股数量: {strategy_2.get('select_num', 'N/A')}")
                print(f"     选股因子: {len(strategy_2.get('factor_list', []))}个")
                print(f"     过滤条件: {len(strategy_2.get('filter_list', []))}个")

            elif hasattr(config_module, 'strategy'):
                # 单阶段策略预览
                strategy = config_module.strategy
                print(f"\n🎯 策略信息:")
                print(f"  名称: {strategy.get('name', 'N/A')}")
                print(f"  持仓周期: {strategy.get('hold_period', 'N/A')}")
                print(f"  选股数量: {strategy.get('select_num', 'N/A')}")

                # 因子数量
                factor_count = len(strategy.get('factor_list', []))
                filter_count = len(strategy.get('filter_list', []))
                print(f"  选股因子: {factor_count}个")
                print(f"  过滤条件: {filter_count}个")
            
            # 择时配置
            if hasattr(config_module, 'equity_timing') and config_module.equity_timing:
                timing = config_module.equity_timing
                print(f"\n⏰ 择时配置:")
                print(f"  方法: {timing.get('name', 'N/A')}")
                print(f"  参数: {timing.get('params', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 预览失败: {e}")
    
    def select_config(self):
        """交互式选择配置"""
        configs = self.scan_configs()
        
        if not configs:
            print("❌ 没有找到可用的配置文件")
            return None
        
        while True:
            self.display_configs(configs)
            
            print("📝 操作选项:")
            print("  输入数字: 选择并运行配置")
            print("  'p' + 数字: 预览配置详情 (如: p1)")
            print("  'r': 刷新配置列表")
            print("  'q': 退出程序")
            
            try:
                choice = input("\n🔢 请选择操作: ").strip()
                
                if choice.lower() == 'q':
                    print("👋 退出程序")
                    return None
                
                if choice.lower() == 'r':
                    print("🔄 刷新配置列表...")
                    configs = self.scan_configs()
                    continue
                
                if choice.lower().startswith('p'):
                    # 预览模式
                    try:
                        preview_index = int(choice[1:].strip())
                        if 0 <= preview_index < len(configs):
                            self.preview_config(configs[preview_index])
                        else:
                            print("❌ 无效的配置索引")
                        input("\n按回车键继续...")
                        continue
                    except (ValueError, IndexError):
                        print("❌ 预览命令格式错误，请使用 'p' + 数字")
                        continue
                
                # 选择配置
                config_index = int(choice)
                if 0 <= config_index < len(configs):
                    selected_config = configs[config_index]
                    
                    print(f"\n✅ 已选择配置: {selected_config['display_name']}")
                    print(f"📊 策略名称: {selected_config['strategy_name']}")
                    
                    # 确认选择
                    confirm = input("确认运行此配置的回测吗? (y/n): ").strip().lower()
                    if confirm in ['y', 'yes', '是', '']:
                        return selected_config
                    else:
                        continue
                else:
                    print("❌ 无效的配置索引")
                    
            except ValueError:
                print("❌ 请输入有效的数字或命令")
            except KeyboardInterrupt:
                print("\n👋 用户取消，退出程序")
                return None

def select_execution_mode():
    """选择执行模式"""
    while True:
        print("\n" + "="*60)
        print("🎯 选择执行模式")
        print("="*60)
        print("\n📋 可用执行模式:")
        print("-" * 60)
        print("  [1] 完整回测 (step1-step4)")
        print("      📊 执行完整的回测流程：数据准备 → 因子计算 → 选股 → 模拟交易")
        print("      ⏰ 适用于：首次运行策略或修改了策略参数")
        print()
        print("  [2] 仅测试择时信号 (step4)")
        print("      📊 基于已有选股结果，只执行模拟交易和择时测试")
        print("      ⏰ 适用于：测试不同择时参数的效果")
        print()

        try:
            choice = input("🔢 请选择执行模式 (1/2): ").strip()

            if choice == '1':
                return 'full'
            elif choice == '2':
                return 'step4_only'
            else:
                print("❌ 请输入 1 或 2")

        except KeyboardInterrupt:
            print("\n👋 用户取消，退出程序")
            return None

def check_step4_prerequisites(config_info):
    """检查step4执行的前置条件"""
    try:
        # 临时切换配置以获取正确的结果路径
        import shutil
        from pathlib import Path

        backup_path = "config_backup.py"
        if Path("config.py").exists():
            shutil.copy("config.py", backup_path)

        try:
            # 如果不是默认配置，临时复制配置文件
            if config_info['name'] != 'default':
                shutil.copy(config_info['file'], "config.py")

            # 重新导入config模块
            if 'config' in sys.modules:
                del sys.modules['config']

            from core.model.backtest_config import load_config
            conf = load_config()

            missing_files = []

            if config_info.get('is_two_stage', False):
                # 两阶段策略需要检查两个策略的选股结果
                import config
                strategy_1 = config.strategy_1
                strategy_2 = config.strategy_2

                # 检查阶段1的选股结果
                stage1_result_file = Path(f"data/回测结果/{strategy_1['name']}/{strategy_1['name']}选股结果.pkl")
                if not stage1_result_file.exists():
                    missing_files.append(f"阶段1选股结果: {stage1_result_file}")

                # 检查阶段2的选股结果
                stage2_result_file = Path(f"data/回测结果/{strategy_2['name']}/{strategy_2['name']}选股结果.pkl")
                if not stage2_result_file.exists():
                    missing_files.append(f"阶段2选股结果: {stage2_result_file}")

            else:
                # 单阶段策略检查
                select_result_file = conf.get_result_folder() / f"{conf.strategy.name}选股结果.pkl"
                if not select_result_file.exists():
                    missing_files.append(f"选股结果: {select_result_file}")

            return len(missing_files) == 0, missing_files

        finally:
            # 恢复原始配置
            if config_info['name'] != 'default' and Path(backup_path).exists():
                shutil.copy(backup_path, "config.py")
                Path(backup_path).unlink()

    except Exception as e:
        return False, [f"检查失败: {e}"]

def run_backtest(config_info, execution_mode='full'):
    """运行回测 - 支持单阶段和两阶段策略，支持不同执行模式"""
    try:
        print(f"\n🚀 开始运行回测...")
        print(f"📁 使用配置: {config_info['display_name']}")
        print(f"🎯 执行模式: {'完整回测' if execution_mode == 'full' else '仅测试择时信号'}")

        if execution_mode == 'step4_only':
            # 检查前置条件
            print("🔍 检查前置条件...")
            prerequisites_ok, missing_files = check_step4_prerequisites(config_info)

            if not prerequisites_ok:
                print("❌ 缺少必要的文件，无法执行step4:")
                for file in missing_files:
                    print(f"   - {file}")
                print("\n💡 建议：请先执行完整回测以生成必要的文件")
                return

            print("✅ 前置条件检查通过")
            run_step4_only(config_info)
        else:
            # 完整回测模式
            if config_info.get('is_two_stage', False):
                run_two_stage_backtest(config_info)
            else:
                run_single_stage_backtest(config_info)

    except Exception as e:
        print(f"❌ 回测执行失败: {e}")
        print(f"💡 请检查配置文件和数据路径是否正确")

def run_step4_only(config_info):
    """只执行step4 - 模拟交易和择时测试"""
    print("🎯 执行择时信号测试...")

    # 临时备份当前config.py
    import shutil
    backup_path = "config_backup.py"
    if Path("config.py").exists():
        shutil.copy("config.py", backup_path)

    try:
        # 如果不是默认配置，临时复制配置文件到config.py
        if config_info['name'] != 'default':
            shutil.copy(config_info['file'], "config.py")
            print(f"✅ 已临时切换到配置: {config_info['name']}")

        # 重新导入config模块
        if 'config' in sys.modules:
            del sys.modules['config']

        # 加载回测配置
        conf = load_config()
        print(f"✅ 配置加载成功!")

        if config_info.get('is_two_stage', False):
            # 两阶段策略：使用最终策略(strategy_2)的选股结果
            import config
            strategy_2 = config.strategy_2
            conf.load_strategy(strategy_2)

            select_result_file = conf.get_result_folder() / f"{conf.strategy.name}选股结果.pkl"
            print(f"📁 使用两阶段策略的最终选股结果: {select_result_file}")
        else:
            # 单阶段策略
            select_result_file = conf.get_result_folder() / f"{conf.strategy.name}选股结果.pkl"
            print(f"📁 使用选股结果: {select_result_file}")

        # 读取选股结果
        import pandas as pd
        select_results = pd.read_pickle(select_result_file)
        print(f"✅ 选股结果加载成功，共 {len(select_results)} 条记录")

        # 执行step4 - 模拟交易
        from program.step4_实盘模拟 import simulate_performance

        print("-" * 36, "模拟交易", "-" * 36)
        simulate_performance(conf, select_results)

        print(f"\n🎉 择时信号测试完成!")
        print(f"📁 结果保存在: {conf.get_result_folder()}")

    finally:
        # 恢复原始config.py
        if config_info['name'] != 'default' and Path(backup_path).exists():
            shutil.copy(backup_path, "config.py")
            Path(backup_path).unlink()  # 删除备份文件
            print(f"✅ 已恢复原始配置")

def run_single_stage_backtest(config_info):
    """运行单阶段回测"""
    print("🎯 执行单阶段回测...")

    # 临时备份当前config.py
    import shutil
    backup_path = "config_backup.py"
    if Path("config.py").exists():
        shutil.copy("config.py", backup_path)

    try:
        # 如果不是默认配置，临时复制配置文件到config.py
        if config_info['name'] != 'default':
            shutil.copy(config_info['file'], "config.py")
            print(f"✅ 已临时切换到配置: {config_info['name']}")

        # 重新导入config模块
        if 'config' in sys.modules:
            del sys.modules['config']

        # 按照原始程序的方式执行回测
        print("🌀 回测系统启动中，请稍候...")

        # 加载回测配置
        conf = load_config()
        print(f"✅ 配置加载成功!")

        # 执行回测步骤 - 完全按照原始程序
        from program.step1_整理数据 import prepare_data
        from program.step2_计算因子 import calculate_factors
        from program.step3_选股 import select_stocks
        from program.step4_实盘模拟 import simulate_performance

        print("-" * 36, "准备数据", "-" * 36)
        prepare_data(conf)

        print("-" * 36, "因子计算", "-" * 36)
        calculate_factors(conf)

        print("-" * 36, "条件选股", "-" * 36)
        select_results = select_stocks(conf)

        print("-" * 36, "模拟交易", "-" * 36)
        simulate_performance(conf, select_results)

        print(f"\n🎉 单阶段回测执行完成!")
        print(f"📁 结果保存在: {conf.get_result_folder()}")

    finally:
        # 恢复原始config.py
        if config_info['name'] != 'default' and Path(backup_path).exists():
            shutil.copy(backup_path, "config.py")
            Path(backup_path).unlink()  # 删除备份文件
            print(f"✅ 已恢复原始配置")

def run_two_stage_backtest(config_info):
    """运行两阶段回测 - 完全按照两阶段回测主程序的逻辑"""
    print("🎯 执行两阶段回测...")

    # 临时备份当前config.py
    import shutil
    backup_path = "config_backup.py"
    if Path("config.py").exists():
        shutil.copy("config.py", backup_path)

    try:
        # 临时复制配置文件到config.py
        shutil.copy(config_info['file'], "config.py")
        print(f"✅ 已临时切换到配置: {config_info['name']}")

        # 重新导入config模块
        if 'config' in sys.modules:
            del sys.modules['config']

        # 导入两阶段策略配置
        import config
        strategy_1 = config.strategy_1
        strategy_2 = config.strategy_2

        print("🌀 两阶段回测系统启动中，请稍候...")
        print("="*80)

        # 加载基础配置
        conf = load_config()

        # ====================================================================================================
        # 🏁 阶段1: 预处理选股 (strategy_1)
        # ====================================================================================================
        print("🚀 阶段1: 开始预处理选股...")
        print(f"策略配置: {strategy_1['name']} (选出最近5天涨幅最多的{strategy_1['select_num']}只股票)")
        print("="*80)

        # 切换到 strategy_1
        print("🔄 加载预处理策略配置...")
        conf.load_strategy(strategy_1)
        print(f"✅ 当前策略: {conf.strategy.name}")

        # 导入必要的模块
        from program.step1_整理数据 import prepare_data
        from program.step2_计算因子 import calculate_factors
        from program.step3_选股 import select_stocks
        from program.step4_实盘模拟 import simulate_performance
        from analyze_select_results import analyze_select_results

        # 1.1 数据准备 (只在第一阶段执行一次)
        print("-" * 36, "准备数据", "-" * 36)
        prepare_data(conf)

        # 1.2 因子计算
        print("-" * 36, "因子计算", "-" * 36)
        calculate_factors(conf)

        # 1.3 选股
        print("-" * 36, "条件选股", "-" * 36)
        stage1_select_results = select_stocks(conf, show_plot=False)  # 不显示图表，节省时间

        # 1.4 分析选股结果，生成热门行业数据
        print("-" * 36, "分析选股结果", "-" * 36)
        print("🔍 正在分析选股结果，生成热门行业数据...")
        analyze_select_results(strategy_1['name'])  # 传入strategy_1的名称
        print("✅ 阶段1完成: 热门行业数据已生成")

        # ====================================================================================================
        # 🏆 阶段2: 最终选股回测 (strategy_2)
        # ====================================================================================================
        print("\n" + "="*80)
        print("🚀 阶段2: 开始最终选股回测...")
        print(f"策略配置: {strategy_2['name']} (基于市值和热门行业等因子选出{strategy_2['select_num']}只股票)")
        print("="*80)

        # 切换到 strategy_2
        print("🔄 切换到最终策略配置...")
        conf.load_strategy(strategy_2)
        print(f"✅ 当前策略: {conf.strategy.name}")

        # 2.1 重新计算因子 (包含热门行业因子)
        print("-" * 36, "因子重新计算", "-" * 36)
        print("💡 重新计算因子 (包含热门行业因子)...")
        calculate_factors(conf)

        # 2.2 最终选股
        print("-" * 36, "最终选股", "-" * 36)
        stage2_select_results = select_stocks(conf, show_plot=False)  # 暂不显示，最后统一显示

        # 2.3 实盘模拟回测
        print("-" * 36, "模拟交易", "-" * 36)
        simulate_performance(conf, stage2_select_results, show_plot=True)  # 显示最终结果

        # ====================================================================================================
        # 🎉 完成总结
        # ====================================================================================================
        print("\n" + "="*80)
        print("🎉 两阶段回测完成！")
        print("="*80)
        print("📊 阶段1结果: 预处理选股完成，生成热门行业数据")
        print(f"   - 策略: {strategy_1['name']}")
        print(f"   - 选股数量: {strategy_1['select_num']}只")
        print(f"   - 结果文件: data/回测结果/{strategy_1['name']}/")

        print("📊 阶段2结果: 最终选股回测完成")
        print(f"   - 策略: {strategy_2['name']}")
        print(f"   - 选股数量: {strategy_2['select_num']}只")
        print(f"   - 结果文件: data/回测结果/{strategy_2['name']}/")

        print("\n💡 使用说明:")
        print("   - 热门行业数据已保存到: data/回测结果/周黎明策略预处理/周黎明策略热门行业.pkl")
        print("   - 最终回测结果已保存到: data/回测结果/周黎明策略/")
        print("   - 可以查看资金曲线图了解策略表现")

        print("\n✅ 程序执行完毕！")

    finally:
        # 恢复原始config.py
        if Path(backup_path).exists():
            shutil.copy(backup_path, "config.py")
            Path(backup_path).unlink()  # 删除备份文件
            print(f"✅ 已恢复原始配置")

def main():
    """主程序入口"""
    print("🚀 邢不行™️选股框架 - 简化版交互式回测程序")
    print("   专注于交互式配置选择，简单可靠")

    selector = SimpleConfigSelector()

    # 交互式选择配置
    selected_config = selector.select_config()

    if selected_config:
        # 选择执行模式
        execution_mode = select_execution_mode()

        if execution_mode:
            # 运行回测
            run_backtest(selected_config, execution_mode)
        else:
            print("👋 未选择执行模式，程序退出")
    else:
        print("👋 未选择配置，程序退出")

if __name__ == '__main__':
    main()
