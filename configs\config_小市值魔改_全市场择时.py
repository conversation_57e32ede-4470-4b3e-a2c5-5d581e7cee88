"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
import os
from pathlib import Path

# ====================================================================================================
# 1️⃣ 回测配置
# ====================================================================================================
# 回测数据的起始时间。如果因子使用滚动计算方法，在回测初期因子值可能为 NaN，实际的首次交易日期可能晚于这个起始时间。
start_date = "2009-01-01"
# 回测数据的结束时间。可以设为 None，表示使用最新数据；也可以指定具体日期，例如 '2024-11-01'。
end_date = None

# ====================================================================================================
# 2️⃣ 数据配置
# ====================================================================================================
# 数据中心的文件夹，使用数据客户端，并订阅相关数据，就不需要再手动指定每一个必要数据
data_center_path = Path("D:/apps/Quantclass/dada_storage")

# 数据源的定义，如果不使用数据客户端的时候，可以手动自定义以下逐个数据源
# ** 股票日线数据 和 指数数据是必须的，其他的数据可以在 data_sources 中定义 **
# (必选) 股票日线数据，全量数据下载链接：https://www.quantclass.cn/data/stock/stock-trading-data
# 参考：https://bbs.quantclass.cn/thread/39599
stock_data_path = data_center_path / "stock-trading-data"
# (必选) 指数数据路径，全量数据下载链接：https://www.quantclass.cn/data/stock/stock-main-index-data
index_data_path = data_center_path / "stock-main-index-data"
# (可选) 财务数据，全量数据下载链接：https://www.quantclass.cn/data/stock/stock-fin-data-xbx
fin_data_path = data_center_path / "stock-fin-data-xbx"

# ====================================================================================================
# 📊 数据处理配置
# ====================================================================================================
# 是否启用行业数据处理
# True: 加载并处理行业分类数据，支持行业轮动等策略（需要数据源包含行业字段）
# False: 仅处理基础价格数据，适合基础选股策略（默认推荐）
use_industry_data = False

# 💡 使用说明：
# - 设置为 True 时，需要确保股票数据文件包含 '新版申万一级行业名称' 和 '新版申万二级行业名称' 字段
# - 设置为 False 时，使用基础数据处理模式，兼容性更好
# - 如果你的策略需要行业轮动、行业分散等功能，请设置为 True

# ====================================================================================================
# 🔄 根据行业数据配置自动调整数据路径
# ====================================================================================================
if use_industry_data:
    # 启用行业数据时，使用增强版数据（包含行业分类信息）
    stock_data_path = data_center_path / "stock-trading-data-pro"
else:
    # 基础模式使用标准数据路径
    pass  # stock_data_path 已在上面定义

# ====================================================================================================
# 3️⃣ 策略配置
# ====================================================================================================
strategy = {
    'name': '小市值魔改_全市场择时',  # 策略名
    'hold_period': '3D',  # 持仓周期，W 代表周，M 代表月，还支持日频：3D、5D、10D
    'select_num': 5,  # 选股数量，可以是整数，也可以是小数，比如 0.1 表示选取 10% 的股票
    "factor_list": [  # 选股因子列表

        ('市值', True, None, 1),
        ('换手率', True, 5, 1),
        ('HRef', True, 55, 1),
        ('Ret', True, 5, 1),
   
    ],
    "filter_list": [

        ('LRef', 55, 'pct:>=0.1'),  # 排除55天接近最低价区间(连跌)
        ('主板因子', True, 'val:==1'),
        ('月份', [1, 4], 'val:!=1'),  # 不在1、4月份选股
        ('收盘价', None, 'val:>=3'),  # 收盘价大于3元
        ('光底阴线', None, 'val: > 1'),  # 在选股数量多的时候，使用效果会好一点，可降低回撤
        ('当前上涨', 60, 'pct:>=0.1'),  # 排除60天接近最低价区间（连跌）,可以排除一直在连续跌停的股票

    ]
}
# strategy = {
#     'name': '策略',  # 策略名
#     'hold_period': 'W',  # 持仓周期，W 代表周，M 代表月，还支持日频：3D、5D、10D
#     'select_num': 10,  # 选股数量，可以是整数，也可以是小数，比如 0.1 表示选取 10% 的股票
#     "factor_list": [  # 选股因子列表
#     ],
#     "filter_list": [
#     ]  # 过滤因子列表
# }



days_listed = 250  # 上市至今交易天数

# excluded_boards = ["bj"]  # 过滤板块，默认不过滤
excluded_boards = ["cyb", "kcb", "bj"]  # 同时过滤创业板和科创板和北交所

# 💡运行提示：
# - 修改hold_period之后，需要执行step2因子计算，不需要再次准备数据
# - 修改select_num之后，只需要再执行step3选股即可，不需要准备数据和计算因子
# - 修改factor_list之后，需要执行step2因子计算，不需要再次准备数据
# - 修改filter_list之后，需要执行step2因子计算，不需要再次准备数据

# 资金曲线再择时配置（非必要，可以为空）
# 用于在回测完成后，对资金曲线进行二次择时，生成动态杠杆
# 可以参考的择时方法：
#   - 移动平均线：根据资金曲线与移动平均线的关系进行择时
#   - 布林带：根据资金曲线与布林带的关系进行择时
#   - 趋势线：根据资金曲线的趋势进行择时
# 参数说明：
#   - name: 择时方法名称
#   - params: 择时方法的参数列表，不同方法参数不同
#     移动平均线: [周期]
#     布林带: [周期, 标准差倍数]
#     趋势线: [周期]
# equity_timing = {"name": "移动平均线", "params": [20]}
# equity_timing = {"name": "MA双均线择时", "params": [8,21]}
equity_timing = {
    #"name": "止损反弹择时", "params": [3,-0.09,10,0.04]
    # "name": "全A打分因子", "params": [30, 5]
    # "name":"移动平均线", "params": [260]
    "name": "止损择时", "params": [3, -0.08]

                 }


# ====================================================================================================
# 4️⃣ 模拟交易配置
# 以下参数几乎不需要改动
# ====================================================================================================
initial_cash = 100_0000  # 初始资金10w
# initial_cash = 1_0000_0000  # 初始资金10w
# 手续费
c_rate = 1.2 / 10000
# 印花税
t_rate = 1 / 1000
# 并行运行的进程数
n_jobs = os.cpu_count() - 1

# =====参数预检查=====
if __name__ == '__main__':
    if Path(stock_data_path).exists() is False:
        print(f"股票日线数据路径不存在：{stock_data_path}，请检查配置或联系助教，程序退出")
        exit()
    if Path(index_data_path).exists() is False:
        print(f"指数数据路径不存在：{index_data_path}，请检查配置或联系助教，程序退出")
        exit()
