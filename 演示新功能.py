"""
演示交互式回测程序的新功能
展示执行模式选择功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def demo_new_features():
    """演示新功能"""
    print("🎯 交互式回测程序新功能演示")
    print("="*50)
    
    print("\n✨ 新增功能：")
    print("1. 执行模式选择")
    print("   - 完整回测 (step1-step4)")
    print("   - 仅测试择时信号 (step4)")
    
    print("\n2. 智能前置条件检查")
    print("   - 自动检查选股结果文件是否存在")
    print("   - 支持单阶段和两阶段策略")
    
    print("\n3. 灵活的择时测试")
    print("   - 基于已有选股结果快速测试择时参数")
    print("   - 节省计算时间，专注择时优化")
    
    print("\n📋 使用场景：")
    print("- 首次运行策略 → 选择'完整回测'")
    print("- 测试择时参数 → 选择'仅测试择时信号'")
    print("- 优化择时策略 → 多次运行'仅测试择时信号'")
    
    print("\n🚀 开始演示...")
    
    # 导入交互式回测程序
    try:
        from 交互式回测程序 import main
        print("✅ 交互式回测程序加载成功")
        
        # 运行主程序
        main()
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        print("💡 请确保所有依赖都已正确安装")

if __name__ == '__main__':
    demo_new_features()
